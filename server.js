// server.js
import express from 'express';
import fetch from 'node-fetch';
import md5 from 'md5';
import cors from 'cors'; // This is technically not strictly needed if serving from same origin, but good practice.

const app = express();
const port = 8000;
const MSPA_API_BASE_URL = 'https://api.iot.the-mspa.com';
const MSPA_APP_ID = 'e1c8e068f9ca11eba4dc0242ac120002';
const MSPA_APP_SECRET = '87025c9ecd18906d27225fe79cb68349';

// Serve static files from the current directory (where index.html is located)
// This is the crucial line that makes the HTML page served from the same origin as the API.
app.use(express.static('.'));

// Enable CORS for all origins (might not be strictly necessary when serving static files like above,
// but useful if you plan to access this proxy from different frontend applications later)
app.use(cors());

app.use(express.json()); // Middleware to parse JSON request bodies

// Helper function to generate nonce
function generateNonce(length = 32) {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Helper function to get current timestamp
function currentTimestamp() {
  return Math.floor(Date.now() / 1000).toString();
}

// Helper function to mask sensitive data in logs
function maskSensitiveData(obj) {
  if (!obj || typeof obj !== 'object') return obj;

  const masked = JSON.parse(JSON.stringify(obj)); // Deep clone

  function maskRecursive(item) {
    if (!item || typeof item !== 'object') return item;

    // Handle arrays
    if (Array.isArray(item)) {
      return item.map(maskRecursive);
    }

    // Handle objects
    for (const key in item) {
      if (item.hasOwnProperty(key)) {
        const value = item[key];

        // Mask email addresses
        if (
          key === 'account' &&
          typeof value === 'string' &&
          value.includes('@')
        ) {
          const [localPart, domain] = value.split('@');
          if (localPart && domain) {
            const maskedLocal =
              localPart.length > 2
                ? localPart.substring(0, 2) + '*'.repeat(localPart.length - 2)
                : localPart.substring(0, 1) + '*';
            const maskedDomain =
              domain.length > 4
                ? '*'.repeat(domain.length - 4) +
                  domain.substring(domain.length - 4)
                : '***.' + domain.split('.').pop();
            item[key] = maskedLocal + '@' + maskedDomain;
          }
        }
        // Mask passwords
        else if (key === 'password') {
          item[key] = '***MASKED***';
        }
        // Mask tokens (show only first 8 characters)
        else if (key === 'token' && typeof value === 'string') {
          item[key] = value.substring(0, 8) + '***MASKED***';
        }
        // Mask visitor_id (show only first 8 characters)
        else if (key === 'visitor_id' && typeof value === 'string') {
          item[key] = value.substring(0, 8) + '***MASKED***';
        }
        // Mask registration_id (show only first 8 characters)
        else if (key === 'registration_id' && typeof value === 'string') {
          item[key] = value.substring(0, 8) + '***MASKED***';
        }
        // Mask enduser_id (show only first 8 characters)
        else if (key === 'enduser_id' && typeof value === 'string') {
          item[key] = value.substring(0, 8) + '***MASKED***';
        }
        // Recursively handle nested objects
        else if (typeof value === 'object' && value !== null) {
          item[key] = maskRecursive(value);
        }
      }
    }

    return item;
  }

  return maskRecursive(masked);
}

// Server-side signature generation (correct MSPA API format)
function buildSignature(appId, appSecret, nonce, ts) {
  // Correct format: appId,appSecret,nonce,ts (comma-separated)
  const raw = `${appId},${appSecret},${nonce},${ts}`;
  return md5(raw).toUpperCase();
}

// Helper function to process request body and hash passwords
function processRequestBody(body, path) {
  if (!body || typeof body !== 'object') return body;

  // Clone the body to avoid modifying the original
  const processedBody = JSON.parse(JSON.stringify(body));

  // Auto-hash passwords for login endpoints
  if (
    (path === '/api/enduser/get_token/' || path === '/api/enduser/get_token') &&
    processedBody.password &&
    typeof processedBody.password === 'string'
  ) {
    // Only hash if it doesn't look like it's already an MD5 hash
    // MD5 hashes are 32 characters of hex
    const isAlreadyMD5 = /^[a-f0-9]{32}$/i.test(processedBody.password);

    if (!isAlreadyMD5) {
      console.log('[Proxy] Auto-hashing password for login request');
      processedBody.password = md5(processedBody.password).toLowerCase();
    }
  }

  return processedBody;
}

// Proxy endpoint
app.post('/api/proxy', async (req, res) => {
  try {
    const { method, path, body, token } = req.body;

    if (!method || !path) {
      return res
        .status(400)
        .json({ error: 'Missing method or path in request.' });
    }

    // Process the request body (auto-hash passwords if needed)
    const processedBody = processRequestBody(body, path);

    const nonce = generateNonce();
    const timestamp = currentTimestamp();

    // Prepare the request body for signature generation
    const requestBody =
      ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) &&
      processedBody &&
      Object.keys(processedBody).length > 0
        ? JSON.stringify(processedBody)
        : '';

    const signature = buildSignature(
      MSPA_APP_ID,
      MSPA_APP_SECRET,
      nonce,
      timestamp
    );

    // Mask sensitive data in signature debug
    let maskedDebugBody = requestBody;
    if (requestBody) {
      try {
        const bodyObj = JSON.parse(requestBody);
        const maskedBody = maskSensitiveData(bodyObj);
        maskedDebugBody = JSON.stringify(maskedBody);
      } catch (e) {
        maskedDebugBody = '***MASKED_BODY***';
      }
    }

    console.log(`[Proxy] Signature Debug:
      - APP_ID: ${MSPA_APP_ID}
      - Nonce: ${nonce}
      - Timestamp: ${timestamp}
      - Method: ${method.toUpperCase()}
      - Path: ${path}
      - Body: ${maskedDebugBody}
      - Generated Signature: ${signature}`);

    const headers = {
      Host: 'api.iot.the-mspa.com',
      appid: MSPA_APP_ID,
      'Content-Type': 'application/json; charset=utf-8',
      lan_code: 'de',
      'User-Agent': 'DongHui/7 CFNetwork/3826.500.111.2.2 Darwin/24.4.0', // Mimic app UA
      Connection: 'keep-alive',
      ts: timestamp,
      nonce: nonce,
      sign: signature,
    };

    if (token) {
      headers['Authorization'] = `token ${token}`;
    }

    // Prepare the options for the fetch request to the MSPA API
    const fetchOptions = {
      method: method,
      headers: headers,
    };

    // Only add body for methods that typically have one
    if (
      ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) &&
      processedBody &&
      Object.keys(processedBody).length > 0
    ) {
      fetchOptions.body = JSON.stringify(processedBody);
    } else if (['GET', 'HEAD'].includes(method.toUpperCase())) {
      // For GET/HEAD, ensure no body is sent even if one was provided in req.body
      delete fetchOptions.body;
    }

    console.log(
      `[Proxy] Forwarding request to MSPA API: ${MSPA_API_BASE_URL}${path}`
    );
    console.log('[Proxy] Request Headers:', headers);

    // Mask sensitive data in request body before logging
    let logBody = 'No body';
    if (fetchOptions.body) {
      try {
        const bodyObj = JSON.parse(fetchOptions.body);
        const maskedBody = maskSensitiveData(bodyObj);
        logBody = JSON.stringify(maskedBody);
      } catch (e) {
        logBody = '***MASKED_BODY***';
      }
    }
    console.log('[Proxy] Request Body:', logBody);

    const mspaResponse = await fetch(
      `${MSPA_API_BASE_URL}${path}`,
      fetchOptions
    );
    const mspaData = await mspaResponse.json();

    console.log('[Proxy] MSPA API Response Status:', mspaResponse.status);

    // Mask sensitive data in response before logging
    const maskedResponseData = maskSensitiveData(mspaData);
    console.log('[Proxy] MSPA API Response Data:', maskedResponseData);

    res.status(mspaResponse.status).json(mspaData);
  } catch (error) {
    console.error('Error during proxy request:', error);
    res.status(500).json({
      error: 'Internal server error during proxy operation.',
      details: error.message,
    });
  }
});

app.listen(port, () => {
  console.log(`MSPA Proxy server listening at http://localhost:${port}`);
  console.log(`Open your browser to http://localhost:${port}/index.html`); // Important!
});
