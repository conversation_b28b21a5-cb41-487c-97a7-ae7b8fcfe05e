// server.js
import express from 'express';
import fetch from 'node-fetch';
import md5 from 'md5';
import cors from 'cors'; // This is technically not strictly needed if serving from same origin, but good practice.

const app = express();
const port = 8000;
const MSPA_API_BASE_URL = "https://api.iot.the-mspa.com";
const MSPA_APP_ID = "e1c8e068f9ca11eba4dc0242ac120002";
const MSPA_APP_SECRET = "87025c9ecd18906d27225fe79cb68349";

// Serve static files from the current directory (where index.html is located)
// This is the crucial line that makes the HTML page served from the same origin as the API.
app.use(express.static('.'));

// Enable CORS for all origins (might not be strictly necessary when serving static files like above,
// but useful if you plan to access this proxy from different frontend applications later)
app.use(cors());

app.use(express.json()); // Middleware to parse JSON request bodies

// Helper function to generate nonce
function generateNonce(length = 32) {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Helper function to get current timestamp
function currentTimestamp() {
    return Math.floor(Date.now() / 1000).toString();
}

// Server-side signature generation (based on Bernd's working solution)
function buildSignature(appId, appSecret, nonce, ts) {
    const raw = `${appId},${appSecret},${nonce},${ts}`;
    return md5(raw).toUpperCase(); // Using the md5 package
}

// Proxy endpoint
app.post('/api/proxy', async (req, res) => {
    try {
        const { method, path, body, token } = req.body;

        if (!method || !path) {
            return res.status(400).json({ error: "Missing method or path in request." });
        }

        const nonce = generateNonce();
        const timestamp = currentTimestamp();
        const signature = buildSignature(MSPA_APP_ID, MSPA_APP_SECRET, nonce, timestamp);

        const headers = {
            "Host": "api.iot.the-mspa.com",
            "appid": MSPA_APP_ID,
            "Content-Type": "application/json; charset=utf-8",
            "lan_code": "de",
            "User-Agent": "DongHui/7 CFNetwork/3826.500.111.2.2 Darwin/24.4.0", // Mimic app UA
            "Connection": "keep-alive",
            "ts": timestamp,
            "nonce": nonce,
            "sign": signature
        };

        if (token) {
            headers['Authorization'] = `token ${token}`;
        }

        // Prepare the options for the fetch request to the MSPA API
        const fetchOptions = {
            method: method,
            headers: headers,
        };

        // Only add body for methods that typically have one
        if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && body && Object.keys(body).length > 0) {
            fetchOptions.body = JSON.stringify(body);
        } else if (['GET', 'HEAD'].includes(method.toUpperCase())) {
            // For GET/HEAD, ensure no body is sent even if one was provided in req.body
            delete fetchOptions.body;
        }


        console.log(`[Proxy] Forwarding request to MSPA API: ${MSPA_API_BASE_URL}${path}`);
        console.log("[Proxy] Request Headers:", headers);
        console.log("[Proxy] Request Body:", fetchOptions.body || 'No body');

        const mspaResponse = await fetch(`${MSPA_API_BASE_URL}${path}`, fetchOptions);
        const mspaData = await mspaResponse.json();

        console.log("[Proxy] MSPA API Response Status:", mspaResponse.status);
        console.log("[Proxy] MSPA API Response Data:", mspaData);

        res.status(mspaResponse.status).json(mspaData);

    } catch (error) {
        console.error("Error during proxy request:", error);
        res.status(500).json({ error: "Internal server error during proxy operation.", details: error.message });
    }
});

app.listen(port, () => {
    console.log(`MSPA Proxy server listening at http://localhost:${port}`);
    console.log(`Open your browser to http://localhost:${port}/index.html`); // Important!
});