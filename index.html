<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MSPA API Tester (via Node.js Proxy)</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f4f4f4;
        color: #333;
      }
      .container {
        background-color: #fff;
        padding: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        max-width: 800px;
        margin: auto;
      }
      h1 {
        text-align: center;
        color: #0056b3;
        margin-bottom: 25px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input[type='text'],
      textarea {
        width: calc(100% - 22px);
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1em;
        box-sizing: border-box;
        font-family: 'Courier New', Courier, monospace;
      }
      textarea {
        min-height: 120px;
        resize: vertical;
      }
      button {
        display: block;
        width: 100%;
        padding: 12px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 1.1em;
        cursor: pointer;
        transition: background-color 0.3s ease;
        margin-top: 20px;
      }
      button:hover {
        background-color: #0056b3;
      }
      .result-group {
        margin-top: 25px;
        border-top: 1px solid #eee;
        padding-top: 20px;
      }
      .response-output {
        background-color: #e9e9e9;
        padding: 15px;
        border-radius: 4px;
        word-break: break-all;
        font-family: 'Courier New', Courier, monospace;
        font-size: 0.9em;
        line-height: 1.4;
        text-align: left;
        border: 1px dashed #ccc;
        white-space: pre-wrap; /* Preserve whitespace and line breaks */
        max-height: 400px;
        overflow-y: auto;
      }
      .status-message {
        margin-top: 10px;
        padding: 10px;
        border-radius: 4px;
        font-weight: bold;
      }
      .status-success {
        background-color: #d4edda;
        color: #155724;
      }
      .status-error {
        background-color: #f8d7da;
        color: #721c24;
      }
      .section-title {
        margin-top: 30px;
        margin-bottom: 15px;
        color: #0056b3;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>MSPA API Tester (via Node.js Proxy)</h1>
      <p>
        This page sends requests through a local Node.js server to bypass CORS
        issues.
      </p>

      <h2 class="section-title">Quick Test Flows</h2>

      <div class="form-group">
        <h3>Real Account Flow:</h3>
        <button
          onclick="realAccountLogin()"
          style="
            margin-right: 10px;
            padding: 10px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          1. Login with Real Account
        </button>
        <button
          onclick="generateShareCode()"
          style="
            margin-right: 10px;
            padding: 10px 15px;
            background: #fd7e14;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          2. Generate Share Code
        </button>
        <button
          onclick="loginAsVisitor()"
          style="
            margin-right: 10px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          3. Login as Visitor
        </button>
        <button
          onclick="grantDeviceAccess()"
          style="
            margin-right: 10px;
            padding: 10px 15px;
            background: #6f42c1;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          4. Grant Device Access
        </button>
      </div>

      <div class="form-group">
        <h3>Device Operations:</h3>
        <button
          onclick="testDeviceStatus()"
          style="
            margin-right: 10px;
            padding: 10px 15px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          Get Device Status
        </button>
        <button
          onclick="testGetDevices()"
          style="
            padding: 10px 15px;
            background: #ffc107;
            color: black;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          "
        >
          Get Device List
        </button>
      </div>

      <div class="form-group">
        <h3>Account Configuration:</h3>
        <label for="userEmail">Your Email:</label>
        <input
          type="email"
          id="userEmail"
          value="<EMAIL>"
          style="width: 100%; margin-bottom: 10px"
        />

        <label for="userPassword">Your Password:</label>
        <input
          type="password"
          id="userPassword"
          value=""
          placeholder="Enter your MSPA account password"
          style="width: 100%; margin-bottom: 10px"
        />
        <small style="color: #666"
          >Enter your actual MSPA account password. It will be automatically MD5
          hashed by the server.</small
        >

        <label for="deviceId">Device ID:</label>
        <input
          type="text"
          id="deviceId"
          value="3c51f69658cf11edaf47aa88ce8b0237"
          style="width: 100%; margin-bottom: 10px"
        />

        <label for="productId">Product ID:</label>
        <input type="text" id="productId" value="O0N301" style="width: 100%" />
      </div>

      <h2 class="section-title">Manual API Request</h2>

      <div class="form-group">
        <label for="method">HTTP Method:</label>
        <input type="text" id="method" value="POST" />
      </div>

      <div class="form-group">
        <label for="path">API Path (e.g., /api/enduser/visitor/):</label>
        <input type="text" id="path" value="/api/enduser/visitor/" />
      </div>

      <div class="form-group">
        <label for="body">Request Body (JSON):</label>
        <textarea id="body">
{
    "visitor_id": "YOUR_VISITOR_ID",
    "registration_id": "YOUR_REGISTRATION_ID",
    "app_id": "e1c8e068f9ca11eba4dc0242ac120002",
    "push_type": "ios",
    "lan_code": "de"
}</textarea
        >
        <small
          >Replace YOUR_VISITOR_ID and YOUR_REGISTRATION_ID with your actual
          captured values from HTTP Catcher. Ensure valid JSON.</small
        >
      </div>

      <div class="form-group">
        <label for="token"
          >Authorization Token (from a successful login response,
          optional):</label
        >
        <input
          type="text"
          id="token"
          placeholder="Paste token here if needed for subsequent requests"
        />
      </div>

      <button onclick="sendApiRequest()">Send API Request</button>

      <h2 class="section-title">Token Information</h2>
      <div class="result-group">
        <div class="form-group">
          <label>Main Account Token:</label>
          <div
            id="mainTokenDisplay"
            style="
              background: #f8f9fa;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-family: monospace;
              word-break: break-all;
              min-height: 20px;
            "
          >
            Not logged in
          </div>
        </div>

        <div class="form-group">
          <label>Visitor Token:</label>
          <div
            id="visitorTokenDisplay"
            style="
              background: #f8f9fa;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-family: monospace;
              word-break: break-all;
              min-height: 20px;
            "
          >
            Not generated
          </div>
          <button
            onclick="copyVisitorToken()"
            style="
              margin-top: 5px;
              padding: 5px 10px;
              background: #007bff;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
            "
          >
            Copy Visitor Token
          </button>
        </div>

        <div class="form-group">
          <label>Share Code:</label>
          <div
            id="shareCodeDisplay"
            style="
              background: #f8f9fa;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-family: monospace;
              word-break: break-all;
              min-height: 20px;
            "
          >
            Not generated
          </div>
        </div>
      </div>

      <h2 class="section-title">API Response</h2>
      <div class="result-group">
        <div id="statusMessage" class="status-message"></div>
        <label>Response Data:</label>
        <div id="apiResponse" class="response-output"></div>
      </div>
    </div>

    <script>
      const PROXY_URL = 'http://localhost:8000/api/proxy'; // Your Node.js proxy endpoint

      // Store tokens for subsequent requests
      let mainAccountToken = null;
      let visitorToken = null;
      let shareCode = null;

      // Simple MD5 implementation for client-side hashing
      function md5(string) {
        function md5cycle(x, k) {
          var a = x[0],
            b = x[1],
            c = x[2],
            d = x[3];
          a = ff(a, b, c, d, k[0], 7, -*********);
          d = ff(d, a, b, c, k[1], 12, -*********);
          c = ff(c, d, a, b, k[2], 17, *********);
          b = ff(b, c, d, a, k[3], 22, -**********);
          // ... (truncated for brevity - this is a basic MD5 implementation)
          x[0] = add32(a, x[0]);
          x[1] = add32(b, x[1]);
          x[2] = add32(c, x[2]);
          x[3] = add32(d, x[3]);
        }

        // For simplicity, we'll use a basic approach
        // In production, use crypto-js library
        var crypto = window.crypto || window.msCrypto;
        if (crypto && crypto.subtle) {
          // Modern browsers - but this is async, so we'll use a simpler approach
        }

        // Simple fallback - user should provide MD5 hash manually
        return string.toLowerCase(); // Placeholder - user should enter actual MD5 hash
      }

      // Generate a random UUID for visitor_id
      function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
          /[xy]/g,
          function (c) {
            var r = (Math.random() * 16) | 0,
              v = c == 'x' ? r : (r & 0x3) | 0x8;
            return v.toString(16).toUpperCase();
          }
        );
      }

      // Update token displays
      function updateTokenDisplays() {
        document.getElementById('mainTokenDisplay').textContent =
          mainAccountToken || 'Not logged in';
        document.getElementById('visitorTokenDisplay').textContent =
          visitorToken || 'Not generated';
        document.getElementById('shareCodeDisplay').textContent =
          shareCode || 'Not generated';
      }

      // Copy visitor token to clipboard
      function copyVisitorToken() {
        if (!visitorToken) {
          alert('No visitor token available to copy!');
          return;
        }

        navigator.clipboard
          .writeText(visitorToken)
          .then(function () {
            alert('Visitor token copied to clipboard!');
          })
          .catch(function (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = visitorToken;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Visitor token copied to clipboard!');
          });
      }

      // Real Account Flow Functions
      async function realAccountLogin() {
        const email = document.getElementById('userEmail').value;
        const password = document.getElementById('userPassword').value;

        if (!email || !password) {
          alert(
            'Please enter your email and password in the configuration section!'
          );
          return;
        }

        // Send the plain password - server will MD5 hash it
        const plainPassword = password;

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/get_token/';
        document.getElementById('body').value = JSON.stringify(
          {
            password: plainPassword,
            account: email,
            registration_id:
              'b6b5c93a5e86f25c980fa1f4615f749b5a31a20e0091ec1434e1dcfea394a04d',
            country: 'NZ',
            app_id: 'e1c8e068f9ca11eba4dc0242ac120002',
            push_type: 'ios',
          },
          null,
          2
        );
        document.getElementById('token').value = '';

        const result = await sendApiRequest();
        if (result && result.data && result.data.token) {
          mainAccountToken = result.data.token;
          document.getElementById('token').value = mainAccountToken;
          updateTokenDisplays();
          alert(
            `Main account login successful! Token: ${mainAccountToken.substring(
              0,
              10
            )}...`
          );
        }
      }

      async function generateShareCode() {
        if (!mainAccountToken) {
          alert('Please login with your main account first!');
          return;
        }

        const deviceId = document.getElementById('deviceId').value;

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/share_code/';
        document.getElementById('body').value = JSON.stringify(
          {
            device_id: deviceId,
          },
          null,
          2
        );
        document.getElementById('token').value = mainAccountToken;

        const result = await sendApiRequest();
        if (result && result.data && result.data.vercode) {
          shareCode = result.data.vercode;
          updateTokenDisplays();
          alert(`Share code generated: ${shareCode}`);
        }
      }

      async function loginAsVisitor() {
        if (!shareCode) {
          alert('Please generate a share code first!');
          return;
        }

        const visitorId = generateUUID();

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/visitor/';
        document.getElementById('body').value = JSON.stringify(
          {
            visitor_id: visitorId,
            registration_id:
              'b6b5c93a5e86f25c980fa1f4615f749b5a31a20e0091ec1434e1dcfea394a04d',
            app_id: 'e1c8e068f9ca11eba4dc0242ac120002',
            push_type: 'ios',
            lan_code: 'en',
          },
          null,
          2
        );
        document.getElementById('token').value = '';

        const result = await sendApiRequest();
        if (result && result.data && result.data.token) {
          visitorToken = result.data.token;
          document.getElementById('token').value = visitorToken;
          updateTokenDisplays();
          alert(
            `Visitor login successful! Token: ${visitorToken.substring(
              0,
              10
            )}... Next: Grant device access!`
          );
        }
      }

      async function grantDeviceAccess() {
        if (!visitorToken) {
          alert('Please login as visitor first!');
          return;
        }

        if (!shareCode) {
          alert('Please generate a share code first!');
          return;
        }

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/grant_device/';
        document.getElementById('body').value = JSON.stringify(
          {
            registration_id:
              'b6b5c93a5e86f25c980fa1f4615f749b5a31a20e0091ec1434e1dcfea394a04d',
            vercode: shareCode,
            push_type: 'ios',
          },
          null,
          2
        );
        document.getElementById('token').value = visitorToken;

        const result = await sendApiRequest();
        if (result && result.code === 0) {
          alert(
            `Device access granted successfully! You can now access device operations.`
          );
        }
      }

      async function testDeviceStatus() {
        const token = visitorToken || mainAccountToken;
        if (!token) {
          alert('Please login first to get a token!');
          return;
        }

        const deviceId = document.getElementById('deviceId').value;
        const productId = document.getElementById('productId').value;

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/device/thing_shadow/';
        document.getElementById('body').value = JSON.stringify(
          {
            device_id: deviceId,
            product_id: productId,
          },
          null,
          2
        );
        document.getElementById('token').value = token;

        await sendApiRequest();
      }

      async function testGetDevices() {
        const token = visitorToken || mainAccountToken;
        if (!token) {
          alert('Please login first to get a token!');
          return;
        }

        document.getElementById('method').value = 'GET';
        document.getElementById('path').value = '/api/enduser/devices/';
        document.getElementById('body').value = '';
        document.getElementById('token').value = token;

        await sendApiRequest();
      }

      async function sendApiRequest() {
        const method = document.getElementById('method').value.toUpperCase();
        const path = document.getElementById('path').value;
        const token = document.getElementById('token').value;
        const apiResponseDiv = document.getElementById('apiResponse');
        const statusMessageDiv = document.getElementById('statusMessage');

        apiResponseDiv.textContent = 'Loading...';
        statusMessageDiv.textContent = '';
        statusMessageDiv.className = 'status-message'; // Reset classes

        let body;
        try {
          const bodyText = document.getElementById('body').value.trim();
          body = bodyText ? JSON.parse(bodyText) : {};
        } catch (e) {
          apiResponseDiv.textContent = 'Error: Invalid JSON in Request Body.';
          statusMessageDiv.textContent = 'Error: Invalid JSON input.';
          statusMessageDiv.classList.add('status-error');
          return;
        }

        if (!method || !path) {
          apiResponseDiv.textContent = 'Error: Method and Path are required.';
          statusMessageDiv.textContent = 'Error: Missing parameters.';
          statusMessageDiv.classList.add('status-error');
          return;
        }

        try {
          const response = await fetch(PROXY_URL, {
            method: 'POST', // Always POST to the proxy
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              method: method,
              path: path,
              body: body,
              token: token,
            }),
          });

          const data = await response.json();

          apiResponseDiv.textContent = JSON.stringify(data, null, 2);

          if (response.ok) {
            // Check if HTTP status is 2xx
            statusMessageDiv.textContent = `Success: HTTP Status ${response.status}`;
            statusMessageDiv.classList.add('status-success');
            return data; // Return the response data for quick test functions
          } else {
            statusMessageDiv.textContent = `Error: HTTP Status ${
              response.status
            } - ${data.message || data.error || 'Unknown Error'}`;
            statusMessageDiv.classList.add('status-error');
            return null;
          }
        } catch (error) {
          console.error('Fetch error:', error);
          apiResponseDiv.textContent = `Error sending request to proxy: ${error.message}`;
          statusMessageDiv.textContent = 'Network or Proxy Error.';
          statusMessageDiv.classList.add('status-error');
          return null;
        }
      }
    </script>
  </body>
</html>
