<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MSPA API Tester (via Node.js Proxy)</title>
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        background-color: #1a1a1a;
        color: #e0e0e0;
      }
      .container {
        background-color: #2d2d2d;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 1000px;
        margin: auto;
        border: 1px solid #404040;
      }
      h1 {
        text-align: center;
        color: #60a5fa;
        margin-bottom: 25px;
      }
      h3 {
        color: #f0f0f0;
        margin-bottom: 15px;
        margin-top: 25px;
      }
      .form-group {
        margin-bottom: 20px;
      }
      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #f0f0f0;
      }
      input[type='text'],
      input[type='email'],
      input[type='password'],
      textarea {
        width: calc(100% - 24px);
        padding: 12px;
        border: 1px solid #555;
        border-radius: 6px;
        font-size: 14px;
        box-sizing: border-box;
        background-color: #3a3a3a;
        color: #e0e0e0;
        font-family: 'Courier New', Courier, monospace;
      }
      input:focus,
      textarea:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }
      textarea {
        min-height: 120px;
        resize: vertical;
      }
      button {
        padding: 12px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        margin: 5px;
        display: inline-block;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #555;
        cursor: not-allowed;
      }
      .result-group {
        margin-top: 30px;
        border-top: 2px solid #404040;
        padding-top: 20px;
      }
      .response-output {
        background-color: #1e1e1e;
        padding: 15px;
        border-radius: 6px;
        word-break: break-all;
        font-family: 'Courier New', Courier, monospace;
        font-size: 12px;
        line-height: 1.4;
        border: 1px solid #555;
        white-space: pre-wrap;
        max-height: 400px;
        overflow-y: auto;
        color: #e0e0e0;
      }
      .status-message {
        margin-top: 15px;
        padding: 12px;
        border-radius: 6px;
        font-weight: 600;
      }
      .status-success {
        background-color: #1e4d2b;
        color: #4ade80;
        border: 1px solid #22c55e;
      }
      .status-error {
        background-color: #4d1e1e;
        color: #f87171;
        border: 1px solid #ef4444;
      }
      .section-title {
        margin-top: 30px;
        margin-bottom: 20px;
        color: #60a5fa;
        border-bottom: 2px solid #404040;
        padding-bottom: 8px;
        font-size: 18px;
        font-weight: 600;
      }
      .info-display {
        background: #1e1e1e;
        padding: 12px;
        border: 1px solid #555;
        border-radius: 6px;
        font-family: 'Courier New', monospace;
        word-break: break-all;
        min-height: 20px;
        color: #e0e0e0;
        margin-bottom: 10px;
      }
      .info-display.empty {
        color: #888;
        font-style: italic;
      }
      small {
        color: #aaa;
      }
      .flow-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
      }
      .device-operations {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>MSPA API Tester (via Node.js Proxy)</h1>
      <p>
        This page sends requests through a local Node.js server to bypass CORS
        issues.
      </p>

      <h2 class="section-title">Account Configuration</h2>

      <div class="form-group">
        <label for="userEmail">Your Email:</label>
        <input
          type="email"
          id="userEmail"
          value="<EMAIL>"
          placeholder="Enter your MSPA account email"
        />
      </div>

      <div class="form-group">
        <label for="userPassword">Your Password:</label>
        <input
          type="password"
          id="userPassword"
          value=""
          placeholder="Enter your MSPA account password"
        />
        <small
          >Enter your actual MSPA account password. It will be automatically MD5
          hashed by the server.</small
        >
      </div>

      <h2 class="section-title">Device Information</h2>

      <div class="form-group">
        <label>Device ID:</label>
        <div id="deviceIdDisplay" class="info-display empty">
          Click "Get Device List" to populate
        </div>
      </div>

      <div class="form-group">
        <label>Product ID:</label>
        <div id="productIdDisplay" class="info-display empty">
          Click "Get Device List" to populate
        </div>
      </div>

      <div class="form-group">
        <label>Share Code (vercode):</label>
        <div id="shareCodeDisplay" class="info-display empty">
          Generate share code first
        </div>
      </div>

      <h2 class="section-title">Authentication Flow</h2>

      <div class="flow-buttons">
        <button onclick="realAccountLogin()" style="background: #28a745">
          1. Login with Real Account
        </button>
        <button
          onclick="testGetDevices()"
          style="background: #ffc107; color: black"
        >
          2. Get Device List
        </button>
        <button onclick="generateShareCode()" style="background: #fd7e14">
          3. Generate Share Code
        </button>
        <button onclick="loginAsVisitor()" style="background: #007bff">
          4. Login as Visitor
        </button>
        <button onclick="grantDeviceAccess()" style="background: #6f42c1">
          5. Grant Device Access
        </button>
      </div>

      <h2 class="section-title">Device Operations</h2>

      <div class="device-operations">
        <button onclick="testDeviceStatus()" style="background: #17a2b8">
          Get Device Status
        </button>
        <button onclick="sendDeviceCommand()" style="background: #dc3545">
          Send Device Command
        </button>
      </div>

      <h2 class="section-title">Manual API Request</h2>

      <div class="form-group">
        <label for="method">HTTP Method:</label>
        <input type="text" id="method" value="POST" />
      </div>

      <div class="form-group">
        <label for="path">API Path (e.g., /api/enduser/visitor/):</label>
        <input type="text" id="path" value="/api/enduser/visitor/" />
      </div>

      <div class="form-group">
        <label for="body">Request Body (JSON):</label>
        <textarea id="body">
{
    "visitor_id": "YOUR_VISITOR_ID",
    "registration_id": "YOUR_REGISTRATION_ID",
    "app_id": "e1c8e068f9ca11eba4dc0242ac120002",
    "push_type": "ios",
    "lan_code": "de"
}</textarea
        >
        <small
          >Replace YOUR_VISITOR_ID and YOUR_REGISTRATION_ID with your actual
          captured values from HTTP Catcher. Ensure valid JSON.</small
        >
      </div>

      <div class="form-group">
        <label for="token"
          >Authorization Token (from a successful login response,
          optional):</label
        >
        <input
          type="text"
          id="token"
          placeholder="Paste token here if needed for subsequent requests"
        />
      </div>

      <button onclick="sendApiRequest()">Send API Request</button>

      <h2 class="section-title">Token Information</h2>
      <div class="result-group">
        <div class="form-group">
          <label>Main Account Token:</label>
          <div id="mainTokenDisplay" class="info-display empty">
            Not logged in
          </div>
        </div>

        <div class="form-group">
          <label>Visitor Token:</label>
          <div id="visitorTokenDisplay" class="info-display empty">
            Not generated
          </div>
          <button onclick="copyVisitorToken()" style="margin-top: 10px">
            Copy Visitor Token
          </button>
        </div>
      </div>

      <h2 class="section-title">API Response</h2>
      <div class="result-group">
        <div id="statusMessage" class="status-message"></div>
        <label>Response Data:</label>
        <div id="apiResponse" class="response-output"></div>
      </div>
    </div>

    <script>
      const PROXY_URL = 'http://localhost:8000/api/proxy'; // Your Node.js proxy endpoint

      // Store tokens and device info for subsequent requests
      let mainAccountToken = null;
      let visitorToken = null;
      let shareCode = null;
      let currentDeviceId = null;
      let currentProductId = null;

      // Simple MD5 implementation for client-side hashing
      function md5(string) {
        function md5cycle(x, k) {
          var a = x[0],
            b = x[1],
            c = x[2],
            d = x[3];
          a = ff(a, b, c, d, k[0], 7, -*********);
          d = ff(d, a, b, c, k[1], 12, -*********);
          c = ff(c, d, a, b, k[2], 17, *********);
          b = ff(b, c, d, a, k[3], 22, -**********);
          // ... (truncated for brevity - this is a basic MD5 implementation)
          x[0] = add32(a, x[0]);
          x[1] = add32(b, x[1]);
          x[2] = add32(c, x[2]);
          x[3] = add32(d, x[3]);
        }

        // For simplicity, we'll use a basic approach
        // In production, use crypto-js library
        var crypto = window.crypto || window.msCrypto;
        if (crypto && crypto.subtle) {
          // Modern browsers - but this is async, so we'll use a simpler approach
        }

        // Simple fallback - user should provide MD5 hash manually
        return string.toLowerCase(); // Placeholder - user should enter actual MD5 hash
      }

      // Generate a random UUID for visitor_id
      function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
          /[xy]/g,
          function (c) {
            var r = (Math.random() * 16) | 0,
              v = c == 'x' ? r : (r & 0x3) | 0x8;
            return v.toString(16).toUpperCase();
          }
        );
      }

      // Update token displays
      function updateTokenDisplays() {
        const mainDisplay = document.getElementById('mainTokenDisplay');
        const visitorDisplay = document.getElementById('visitorTokenDisplay');

        if (mainAccountToken) {
          mainDisplay.textContent = mainAccountToken;
          mainDisplay.classList.remove('empty');
        } else {
          mainDisplay.textContent = 'Not logged in';
          mainDisplay.classList.add('empty');
        }

        if (visitorToken) {
          visitorDisplay.textContent = visitorToken;
          visitorDisplay.classList.remove('empty');
        } else {
          visitorDisplay.textContent = 'Not generated';
          visitorDisplay.classList.add('empty');
        }
      }

      // Update device information displays
      function updateDeviceDisplays() {
        const deviceIdDisplay = document.getElementById('deviceIdDisplay');
        const productIdDisplay = document.getElementById('productIdDisplay');
        const shareCodeDisplay = document.getElementById('shareCodeDisplay');

        if (currentDeviceId) {
          deviceIdDisplay.textContent = currentDeviceId;
          deviceIdDisplay.classList.remove('empty');
        }

        if (currentProductId) {
          productIdDisplay.textContent = currentProductId;
          productIdDisplay.classList.remove('empty');
        }

        if (shareCode) {
          shareCodeDisplay.textContent = shareCode;
          shareCodeDisplay.classList.remove('empty');
        } else {
          shareCodeDisplay.textContent = 'Generate share code first';
          shareCodeDisplay.classList.add('empty');
        }
      }

      // Copy visitor token to clipboard
      function copyVisitorToken() {
        if (!visitorToken) {
          alert('No visitor token available to copy!');
          return;
        }

        navigator.clipboard
          .writeText(visitorToken)
          .then(function () {
            alert('Visitor token copied to clipboard!');
          })
          .catch(function (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = visitorToken;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Visitor token copied to clipboard!');
          });
      }

      // Real Account Flow Functions
      async function realAccountLogin() {
        const email = document.getElementById('userEmail').value;
        const password = document.getElementById('userPassword').value;

        if (!email || !password) {
          alert(
            'Please enter your email and password in the configuration section!'
          );
          return;
        }

        // Send the plain password - server will MD5 hash it
        const plainPassword = password;

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/get_token/';
        document.getElementById('body').value = JSON.stringify(
          {
            password: plainPassword,
            account: email,
            registration_id:
              'b6b5c93a5e86f25c980fa1f4615f749b5a31a20e0091ec1434e1dcfea394a04d',
            country: 'NZ',
            app_id: 'e1c8e068f9ca11eba4dc0242ac120002',
            push_type: 'ios',
          },
          null,
          2
        );
        document.getElementById('token').value = '';

        const result = await sendApiRequest();
        if (result && result.data && result.data.token) {
          mainAccountToken = result.data.token;
          document.getElementById('token').value = mainAccountToken;
          updateTokenDisplays();
          alert(
            `Main account login successful! Token: ${mainAccountToken.substring(
              0,
              10
            )}...`
          );
        }
      }

      async function generateShareCode() {
        if (!mainAccountToken) {
          alert('Please login with your main account first!');
          return;
        }

        if (!currentDeviceId) {
          alert('Please get device list first to populate device ID!');
          return;
        }

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/share_code/';
        document.getElementById('body').value = JSON.stringify(
          {
            device_id: currentDeviceId,
          },
          null,
          2
        );
        document.getElementById('token').value = mainAccountToken;

        const result = await sendApiRequest();
        if (result && result.data && result.data.vercode) {
          shareCode = result.data.vercode;
          updateDeviceDisplays();
          alert(`Share code generated: ${shareCode}`);
        }
      }

      async function loginAsVisitor() {
        if (!shareCode) {
          alert('Please generate a share code first!');
          return;
        }

        const visitorId = generateUUID();

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/visitor/';
        document.getElementById('body').value = JSON.stringify(
          {
            visitor_id: visitorId,
            registration_id:
              'b6b5c93a5e86f25c980fa1f4615f749b5a31a20e0091ec1434e1dcfea394a04d',
            app_id: 'e1c8e068f9ca11eba4dc0242ac120002',
            push_type: 'ios',
            lan_code: 'en',
          },
          null,
          2
        );
        document.getElementById('token').value = '';

        const result = await sendApiRequest();
        if (result && result.data && result.data.token) {
          visitorToken = result.data.token;
          document.getElementById('token').value = visitorToken;
          updateTokenDisplays();
          alert(
            `Visitor login successful! Token: ${visitorToken.substring(
              0,
              10
            )}... Next: Grant device access!`
          );
        }
      }

      async function grantDeviceAccess() {
        if (!visitorToken) {
          alert('Please login as visitor first!');
          return;
        }

        if (!shareCode) {
          alert('Please generate a share code first!');
          return;
        }

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/enduser/grant_device/';
        document.getElementById('body').value = JSON.stringify(
          {
            registration_id:
              'b6b5c93a5e86f25c980fa1f4615f749b5a31a20e0091ec1434e1dcfea394a04d',
            vercode: shareCode,
            push_type: 'ios',
          },
          null,
          2
        );
        document.getElementById('token').value = visitorToken;

        const result = await sendApiRequest();
        if (result && result.code === 0) {
          alert(
            `Device access granted successfully! You can now access device operations.`
          );
        }
      }

      async function testDeviceStatus() {
        const token = visitorToken || mainAccountToken;
        if (!token) {
          alert('Please login first to get a token!');
          return;
        }

        if (!currentDeviceId || !currentProductId) {
          alert(
            'Please get device list first to populate device and product IDs!'
          );
          return;
        }

        document.getElementById('method').value = 'POST';
        document.getElementById('path').value = '/api/device/thing_shadow/';
        document.getElementById('body').value = JSON.stringify(
          {
            device_id: currentDeviceId,
            product_id: currentProductId,
          },
          null,
          2
        );
        document.getElementById('token').value = token;

        await sendApiRequest();
      }

      async function testGetDevices() {
        const token = visitorToken || mainAccountToken;
        if (!token) {
          alert('Please login first to get a token!');
          return;
        }

        document.getElementById('method').value = 'GET';
        document.getElementById('path').value = '/api/enduser/devices/';
        document.getElementById('body').value = '';
        document.getElementById('token').value = token;

        const result = await sendApiRequest();

        // Extract device information from response
        if (
          result &&
          result.data &&
          result.data.list &&
          result.data.list.length > 0
        ) {
          const device = result.data.list[0]; // Use first device
          currentDeviceId = device.device_id;
          currentProductId = device.product_id;
          updateDeviceDisplays();
          alert(
            `Device found: ${
              device.name || device.device_alias || 'Unknown'
            }\nDevice ID: ${currentDeviceId}\nProduct ID: ${currentProductId}`
          );
        }
      }

      async function sendApiRequest() {
        const method = document.getElementById('method').value.toUpperCase();
        const path = document.getElementById('path').value;
        const token = document.getElementById('token').value;
        const apiResponseDiv = document.getElementById('apiResponse');
        const statusMessageDiv = document.getElementById('statusMessage');

        apiResponseDiv.textContent = 'Loading...';
        statusMessageDiv.textContent = '';
        statusMessageDiv.className = 'status-message'; // Reset classes

        let body;
        try {
          const bodyText = document.getElementById('body').value.trim();
          body = bodyText ? JSON.parse(bodyText) : {};
        } catch (e) {
          apiResponseDiv.textContent = 'Error: Invalid JSON in Request Body.';
          statusMessageDiv.textContent = 'Error: Invalid JSON input.';
          statusMessageDiv.classList.add('status-error');
          return;
        }

        if (!method || !path) {
          apiResponseDiv.textContent = 'Error: Method and Path are required.';
          statusMessageDiv.textContent = 'Error: Missing parameters.';
          statusMessageDiv.classList.add('status-error');
          return;
        }

        try {
          const response = await fetch(PROXY_URL, {
            method: 'POST', // Always POST to the proxy
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              method: method,
              path: path,
              body: body,
              token: token,
            }),
          });

          const data = await response.json();

          apiResponseDiv.textContent = JSON.stringify(data, null, 2);

          if (response.ok) {
            // Check if HTTP status is 2xx
            statusMessageDiv.textContent = `Success: HTTP Status ${response.status}`;
            statusMessageDiv.classList.add('status-success');
            return data; // Return the response data for quick test functions
          } else {
            statusMessageDiv.textContent = `Error: HTTP Status ${
              response.status
            } - ${data.message || data.error || 'Unknown Error'}`;
            statusMessageDiv.classList.add('status-error');
            return null;
          }
        } catch (error) {
          console.error('Fetch error:', error);
          apiResponseDiv.textContent = `Error sending request to proxy: ${error.message}`;
          statusMessageDiv.textContent = 'Network or Proxy Error.';
          statusMessageDiv.classList.add('status-error');
          return null;
        }
      }
    </script>
  </body>
</html>
