<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSPA API Tester (via Node.js Proxy)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            background-color: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: auto;
        }
        h1 {
            text-align: center;
            color: #0056b3;
            margin-bottom: 25px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: calc(100% - 22px);
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1em;
            box-sizing: border-box;
            font-family: 'Courier New', Courier, monospace;
        }
        textarea {
            min-height: 120px;
            resize: vertical;
        }
        button {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 20px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result-group {
            margin-top: 25px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .response-output {
            background-color: #e9e9e9;
            padding: 15px;
            border-radius: 4px;
            word-break: break-all;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.9em;
            line-height: 1.4;
            text-align: left;
            border: 1px dashed #ccc;
            white-space: pre-wrap; /* Preserve whitespace and line breaks */
            max-height: 400px;
            overflow-y: auto;
        }
        .status-message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .section-title {
            margin-top: 30px;
            margin-bottom: 15px;
            color: #0056b3;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MSPA API Tester (via Node.js Proxy)</h1>
        <p>This page sends requests through a local Node.js server to bypass CORS issues.</p>

        <h2 class="section-title">API Request Parameters</h2>

        <div class="form-group">
            <label for="method">HTTP Method:</label>
            <input type="text" id="method" value="POST">
        </div>

        <div class="form-group">
            <label for="path">API Path (e.g., /api/enduser/visitor/):</label>
            <input type="text" id="path" value="/api/enduser/visitor/">
        </div>

        <div class="form-group">
            <label for="body">Request Body (JSON):</label>
            <textarea id="body">
{
    "visitor_id": "YOUR_VISITOR_ID",
    "registration_id": "YOUR_REGISTRATION_ID",
    "app_id": "e1c8e068f9ca11eba4dc0242ac120002",
    "push_type": "ios",
    "lan_code": "de"
}</textarea>
            <small>Replace YOUR_VISITOR_ID and YOUR_REGISTRATION_ID with your actual captured values from HTTP Catcher. Ensure valid JSON.</small>
        </div>

        <div class="form-group">
            <label for="token">Authorization Token (from a successful login response, optional):</label>
            <input type="text" id="token" placeholder="Paste token here if needed for subsequent requests">
        </div>

        <button onclick="sendApiRequest()">Send API Request</button>

        <h2 class="section-title">API Response</h2>
        <div class="result-group">
            <div id="statusMessage" class="status-message"></div>
            <label>Response Data:</label>
            <div id="apiResponse" class="response-output"></div>
        </div>
    </div>

    <script>
        const PROXY_URL = 'http://localhost:3000/api/proxy'; // Your Node.js proxy endpoint

        async function sendApiRequest() {
            const method = document.getElementById('method').value.toUpperCase();
            const path = document.getElementById('path').value;
            const token = document.getElementById('token').value;
            const apiResponseDiv = document.getElementById('apiResponse');
            const statusMessageDiv = document.getElementById('statusMessage');

            apiResponseDiv.textContent = 'Loading...';
            statusMessageDiv.textContent = '';
            statusMessageDiv.className = 'status-message'; // Reset classes

            let body;
            try {
                const bodyText = document.getElementById('body').value.trim();
                body = bodyText ? JSON.parse(bodyText) : {};
            } catch (e) {
                apiResponseDiv.textContent = 'Error: Invalid JSON in Request Body.';
                statusMessageDiv.textContent = 'Error: Invalid JSON input.';
                statusMessageDiv.classList.add('status-error');
                return;
            }

            if (!method || !path) {
                apiResponseDiv.textContent = 'Error: Method and Path are required.';
                statusMessageDiv.textContent = 'Error: Missing parameters.';
                statusMessageDiv.classList.add('status-error');
                return;
            }

            try {
                const response = await fetch(PROXY_URL, {
                    method: 'POST', // Always POST to the proxy
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        method: method,
                        path: path,
                        body: body,
                        token: token,
                    }),
                });

                const data = await response.json();

                apiResponseDiv.textContent = JSON.stringify(data, null, 2);

                if (response.ok) { // Check if HTTP status is 2xx
                    statusMessageDiv.textContent = `Success: HTTP Status ${response.status}`;
                    statusMessageDiv.classList.add('status-success');
                } else {
                    statusMessageDiv.textContent = `Error: HTTP Status ${response.status} - ${data.message || data.error || 'Unknown Error'}`;
                    statusMessageDiv.classList.add('status-error');
                }

            } catch (error) {
                console.error('Fetch error:', error);
                apiResponseDiv.textContent = `Error sending request to proxy: ${error.message}`;
                statusMessageDiv.textContent = 'Network or Proxy Error.';
                statusMessageDiv.classList.add('status-error');
            }
        }
    </script>
</body>
</html>